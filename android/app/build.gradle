plugins {
    id "com.android.application"
    id "kotlin-android"
    id 'com.google.gms.google-services'
    id "dev.flutter.flutter-gradle-plugin"
}
dependencies {
    // Import the Firebase BoM
    implementation platform('com.google.firebase:firebase-bom:33.1.2')


    // TODO: Add the dependencies for Firebase products you want to use
    // When using the BoM, don't specify versions in Firebase dependencies
    // https://firebase.google.com/docs/android/setup#available-libraries
}
android {
    namespace = "com.rixxsol.foilup.foilup"
    compileSdk = 35
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'  // Update this to match Java version
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.rixxsol.foilup.foilup"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 9
        versionName "3.0.0"
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    buildFeatures {
        viewBinding true
    }

    dependencies {
        implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
        implementation "androidx.lifecycle:lifecycle-common-java8:2.6.2"
        implementation "androidx.fragment:fragment-ktx:1.6.2"
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/*.kotlin_module'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation "androidx.appcompat:appcompat:1.6.1"
    implementation "androidx.core:core-ktx:1.12.0"
    implementation "org.jetbrains.kotlinx:kotlinx-metadata-jvm:0.7.0"
}

// TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).

// You can update the following values to match your application needs.
// For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
