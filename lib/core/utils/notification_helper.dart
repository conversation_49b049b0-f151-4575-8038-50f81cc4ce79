import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:get/get.dart';
import 'permission_manager.dart';

class NotificationHelper {
  static NotificationHelper? _instance;
  static NotificationHelper get instance => _instance ??= NotificationHelper._();
  NotificationHelper._();

  /// Request notification permission for specific features
  /// This should be called when user actually needs notifications, not on app startup
  static Future<bool> requestNotificationForFeature({
    required String featureName,
    required String description,
  }) async {
    return await PermissionManager.requestNotificationPermission(
      context: 'Enable notifications for $featureName: $description',
      showDialog: true,
    );
  }

  /// Send a local notification (only if permission is granted)
  static Future<void> sendLocalNotification({
    required String title,
    required String body,
    String? channelKey,
    Map<String, String>? payload,
  }) async {
    try {
      bool isAllowed = await AwesomeNotifications().isNotificationAllowed();
      if (!isAllowed) {
        print('Notification permission not granted, skipping notification');
        return;
      }

      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: DateTime.now().millisecondsSinceEpoch.remainder(100000),
          channelKey: channelKey ?? 'default_channel_id',
          title: title,
          body: body,
          payload: payload,
        ),
      );
    } catch (e) {
      print('Error sending notification: $e');
    }
  }

  /// Request notification permission for buddy requests
  static Future<bool> requestForBuddyRequests() async {
    return await requestNotificationForFeature(
      featureName: 'Buddy Requests',
      description: 'Get notified when someone wants to be your foiling buddy or invites you to a session',
    );
  }

  /// Request notification permission for session updates
  static Future<bool> requestForSessionUpdates() async {
    return await requestNotificationForFeature(
      featureName: 'Session Updates',
      description: 'Get notified about session changes, weather updates, and spot conditions',
    );
  }

  /// Request notification permission for spot updates
  static Future<bool> requestForSpotUpdates() async {
    return await requestNotificationForFeature(
      featureName: 'Spot Updates',
      description: 'Get notified about new spots, spot conditions, and nearby foilers',
    );
  }

  /// Check if notifications are enabled
  static Future<bool> areNotificationsEnabled() async {
    return await AwesomeNotifications().isNotificationAllowed();
  }

  /// Show notification settings in app (not phone settings)
  static void showNotificationSettings() {
    Get.dialog(
      AlertDialog(
        title: const Text('Notification Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Manage your notification preferences:'),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('Buddy Requests'),
              subtitle: const Text('Get notified about buddy requests'),
              trailing: Switch(
                value: true, // You can track this state
                onChanged: (value) {
                  if (value) {
                    requestForBuddyRequests();
                  }
                },
              ),
            ),
            ListTile(
              title: const Text('Session Updates'),
              subtitle: const Text('Get notified about session changes'),
              trailing: Switch(
                value: true, // You can track this state
                onChanged: (value) {
                  if (value) {
                    requestForSessionUpdates();
                  }
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
