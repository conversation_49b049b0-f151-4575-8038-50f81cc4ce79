import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class PermissionManager {
  static PermissionManager? _instance;
  static PermissionManager get instance => _instance ??= PermissionManager._();
  PermissionManager._();

  // Track if we've already shown permission dialogs to avoid spam
  static bool _hasShownNotificationDialog = false;
  static bool _hasShownLocationDialog = false;

  /// Request notification permission with user context
  /// Only call this when user actually needs notifications (not on app startup)
  static Future<bool> requestNotificationPermission({
    String? context,
    bool showDialog = true,
  }) async {
    try {
      // Check if already allowed
      bool isAllowed = await AwesomeNotifications().isNotificationAllowed();
      if (isAllowed) return true;

      // Don't spam user with permission requests
      if (_hasShownNotificationDialog && !showDialog) {
        return false;
      }

      if (showDialog) {
        // Show custom dialog explaining why we need permission
        bool? userConsent = await _showPermissionDialog(
          title: 'Enable Notifications',
          message: context ?? 
            'Allow notifications to stay updated with session invites, buddy requests, and important updates.',
          permissionType: 'Notifications',
        );

        if (userConsent != true) return false;
      }

      _hasShownNotificationDialog = true;
      
      // Request permission
      bool granted = await AwesomeNotifications().requestPermissionToSendNotifications();
      
      if (!granted && showDialog) {
        _showPermissionDeniedDialog('Notifications');
      }
      
      return granted;
    } catch (e) {
      print('Error requesting notification permission: $e');
      return false;
    }
  }

  /// Request location permission with user context
  static Future<LocationPermission> requestLocationPermission({
    String? context,
    bool showDialog = true,
  }) async {
    try {
      // Check current permission status
      LocationPermission permission = await Geolocator.checkPermission();
      
      if (permission == LocationPermission.always || 
          permission == LocationPermission.whileInUse) {
        return permission;
      }

      // Don't spam user with permission requests
      if (_hasShownLocationDialog && !showDialog) {
        return permission;
      }

      if (showDialog && permission == LocationPermission.denied) {
        // Show custom dialog explaining why we need permission
        bool? userConsent = await _showPermissionDialog(
          title: 'Enable Location Access',
          message: context ?? 
            'Allow location access to find nearby foiling spots, track sessions, and connect with local foilers.',
          permissionType: 'Location',
        );

        if (userConsent != true) return permission;
      }

      _hasShownLocationDialog = true;

      // Request permission
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      // Handle permanently denied
      if (permission == LocationPermission.deniedForever && showDialog) {
        _showPermissionPermanentlyDeniedDialog('Location');
      }

      return permission;
    } catch (e) {
      print('Error requesting location permission: $e');
      return LocationPermission.denied;
    }
  }

  /// Check if location services are enabled
  static Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  /// Show custom permission dialog
  static Future<bool?> _showPermissionDialog({
    required String title,
    required String message,
    required String permissionType,
  }) async {
    return await Get.dialog<bool>(
      AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Not Now'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            child: const Text('Allow'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  /// Show permission denied dialog
  static void _showPermissionDeniedDialog(String permissionType) {
    Get.dialog(
      AlertDialog(
        title: Text('$permissionType Permission Denied'),
        content: Text(
          'You can enable $permissionType permission later in the app settings if you change your mind.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show permanently denied dialog with option to open settings
  static void _showPermissionPermanentlyDeniedDialog(String permissionType) {
    Get.dialog(
      AlertDialog(
        title: Text('$permissionType Permission Required'),
        content: Text(
          '$permissionType permission is permanently denied. Please enable it in app settings to use this feature.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              openAppSettings(); // This is the only place we should open settings
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  /// Reset permission dialog flags (useful for testing or when user explicitly wants to retry)
  static void resetPermissionDialogs() {
    _hasShownNotificationDialog = false;
    _hasShownLocationDialog = false;
  }

  /// Check all permissions status
  static Future<Map<String, bool>> checkAllPermissions() async {
    bool notifications = await AwesomeNotifications().isNotificationAllowed();
    LocationPermission location = await Geolocator.checkPermission();
    bool locationEnabled = location == LocationPermission.always || 
                          location == LocationPermission.whileInUse;

    return {
      'notifications': notifications,
      'location': locationEnabled,
      'locationService': await isLocationServiceEnabled(),
    };
  }
}
