import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foilup/core/utils/utils.dart';
import 'package:foilup/view/dashboard_modules/sessions/controller/session_controller.dart';
import 'package:foilup/view/dashboard_modules/sessions/model/fav_session_spot.dart';
import 'package:foilup/view/dashboard_modules/sessions/model/history_session_model.dart';
import 'package:foilup/view/dashboard_modules/sessions/view/history_sessions_view.dart';
import 'package:foilup/view/dashboard_modules/sessions/view/schedule_sessions_view.dart';

import 'package:foilup/view/dashboard_modules/sessions/view/track_session_view.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/icon_button.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';
import '../model/session_model.dart';
import '../model/session_tracking_model.dart';

class SessionListView extends StatefulWidget {
  SessionListView({super.key});

  @override
  State<SessionListView> createState() => _SessionListViewState();
}

class _SessionListViewState extends State<SessionListView>
    with SingleTickerProviderStateMixin {
  late TabController tabController;
  late SessionController sessionController;
  List<DateTime> monthYearList = [];
  String? formattedMonthYear;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    tabController = TabController(
      length: 3,
      vsync: this,
    );
    sessionController = Get.find();
    sessionController.selectedDate = DateTime.now();
    sessionController.year.value = DateTime.now().year;
    formattedMonthYear = DateFormat('MMMM yyyy').format(DateTime.now());
    String apiFormattedDate =
        DateFormat('MM-yyyy').format(sessionController.selectedDate);
    sessionController.fetchTrackGraphDataByMonth(apiFormattedDate);
    sessionController.fetchLongestRun();
    sessionController.fetchNumberSession();
  }

  void goToPreviousMonth() {
    setState(() {
      sessionController.selectedDate = DateTime(
          sessionController.selectedDate.year,
          sessionController.selectedDate.month - 1,
          1);
      formattedMonthYear =
          DateFormat('MMMM yyyy').format(sessionController.selectedDate);
      String apiFormattedDate =
          DateFormat('MM-yyyy').format(sessionController.selectedDate);
      sessionController.fetchTrackGraphDataByMonth(apiFormattedDate);
    });
  }

  void goToNextMonth() {
    setState(() {
      sessionController.selectedDate = DateTime(
          sessionController.selectedDate.year,
          sessionController.selectedDate.month + 1,
          1);
      formattedMonthYear =
          DateFormat('MMMM yyyy').format(sessionController.selectedDate);
      String apiFormattedDate =
          DateFormat('MM-yyyy').format(sessionController.selectedDate);
      sessionController.fetchTrackGraphDataByMonth(apiFormattedDate);
    });
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
        length: 3,
        child: Scaffold(
          bottomNavigationBar: Obx(() {
            return sessionController.activeTabIndex.value == 2
                ? const SizedBox
                    .shrink() // Hide bottom navigation bar for the third tab
                : Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: Row(
                      children: [
                        Expanded(
                            child: ButtonWithIcon(
                                title: "Start Now",
                                icon: CupertinoIcons.play_arrow,
                                onTap: () {
                                  sessionController.fromStartNowScheduleView();
                                },
                                mainColor: ColorConstants.secondaryColor)),
                        const SizedBox(
                          width: 10,
                        ),
                        Expanded(
                            child: ButtonWithIcon(
                          title: "Schedule",
                          icon: CupertinoIcons.plus_app,
                          onTap: () {
                            sessionController.goToAddScheduleView();
                          },
                          mainColor: Colors.white,
                          bgColor: ColorConstants.secondaryColor,
                        ))
                      ],
                    ),
                  );
          }),
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15.0),
            child: Column(
              children: [tabHeader(), Widgets.heightSpaceH1, tabBody()],
            ),
          ),
        ));
  }

  tabBody() {
    return Expanded(
      child: TabBarView(
        controller: tabController,
        children: [
          const HistorySessionsView(),
          const ScheduleSessionsView(),
          statisticSectionTab(),
        ],
      ),
    );
  }

  tabHeader() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),

      decoration: BoxDecoration(
        color: const Color(0xffeeeaea),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: TabBar(
        onTap: (index) => sessionController.updateTabIndex(index),
        controller: tabController,
        dividerHeight: 0,
        // Adjust the padding here to cover the left and right spacing of the indicator
        indicatorPadding:
            const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
        labelColor: ColorConstants.secondaryColor,
        indicatorColor: ColorConstants.secondaryColor,
        labelStyle: const TextStyle(
          fontSize: 12.0,

          color: Colors.black,
        ),
        unselectedLabelColor: Colors.black,
        unselectedLabelStyle: const TextStyle(
          fontSize: 12.0,
        ),
        tabs: const [
          Tab(text: 'History'),
          Tab(text: 'Scheduled'),
          Tab(text: 'Statistics'),
        ],
      ),
    );
  }

  statisticSectionTab() {
    return GetBuilder(
        init: sessionController,
        builder: (_) {
          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                sessionController.isTrackGraphLoading
                    ? SessionGraphSkeleton()
                    : pumpsMood(),
                Widgets.heightSpaceH2,
                longestRuns(),
                Widgets.heightSpaceH2,
                numberOfSessions(),
                Widgets.heightSpaceH2,
                topFavSpots(),
                Widgets.heightSpaceH2,
              ],
            ),
          );
        });
  }

  topFavSpots() {
    return Obx(() {
      return Container(
          width: 1.sw,
          padding: const EdgeInsets.all(15),
          decoration: Widgets.blockDecoration,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Texts.textBlock("Your Favorite Spots",
                  fontFamily: "RobotoRegular",
                  size: 17,
                  fontWeight: FontWeight.w300),
              Widgets.heightSpaceH2,
              sessionController.isLongestRunLoading.value
                  ? const ShimmerListSkeleton()
                  : sessionController.favSessionsList!.isNotEmpty
                      ? ListView.separated(
                          physics: const BouncingScrollPhysics(),
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            FavSessionSpot? session =
                                sessionController.favSessionsList?[index];
                            return Widgets.sessionFavSportCard(
                                session: session, index: index + 1);
                          },
                          separatorBuilder: (context, index) {
                            return Divider(
                              color: ColorConstants.backgroundColor,
                            );
                          },
                          itemCount:
                              sessionController.favSessionsList?.length ?? 0)
                      : Column(children: [
                          const SizedBox(
                            height: 50,
                          ),
                          Widgets.noFound()
                        ]),
            ],
          ));
    });
  }

  longestRuns() {
    return Obx(() {
      return Container(
          width: 1.sw,
          padding: const EdgeInsets.all(15),
          decoration: Widgets.blockDecoration,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Texts.textBlock("Longest Run (in seconds)",
                  fontFamily: "RobotoRegular",
                  size: 17,
                  fontWeight: FontWeight.w300),

              Widgets.heightSpaceH2,
              sessionController.longestData.value.isEmptySkillLevel==false? Padding(
                padding: const EdgeInsets.symmetric(vertical: 2.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Label Column
                    Expanded(
                      flex: 2,
                      child: Text(
                        "My Skill\nLevel",
                        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                      ),
                    ),
                    // Me and Average Progress Bars
                    Expanded(
                      flex: 6,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            sessionController.longestData.value.userLongestRun.toString(),
                            style: TextStyle(fontSize: 9),
                          ),
                          const SizedBox(height: 5),
                          Row(
                            children: [

                              Widgets.buildProgressBar( sessionController.longestData.value.userLongestRun ?? 0, Colors.cyan,  sessionController
                                  .longestData.value.statistics?.skillLevel?.maximum ??
                                  0)
                            ],
                          ),
                          const SizedBox(height: 3),
                          Text(
                            sessionController
                                .longestData.value.statistics!.skillLevel!.average.toString(),
                            style: TextStyle(fontSize: 9),
                          ),
                          const SizedBox(height: 5),
                          Row(
                            children: [

                             Widgets.buildProgressBar(
                                  sessionController
                                      .longestData.value.statistics?.skillLevel?.average ??
                                      0, Colors.grey.shade300, sessionController
                                  .longestData.value.statistics?.skillLevel?.maximum ??
                                  0),
                              const SizedBox(width: 5)
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    // Maximum Value Circle
                    Expanded(
                      flex: 2,
                      child: CircleAvatar(
                        backgroundColor: Colors.orangeAccent,
                        radius: 25,
                        child: Text(
                          sessionController
                              .longestData.value.statistics!.skillLevel!.maximum.toString(),
                          style:
                          const TextStyle(fontSize: 10, fontWeight: FontWeight.w800),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ):Padding(
                padding: const EdgeInsets.symmetric(vertical: 2.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Label Column
                    Expanded(
                      flex: 2,
                      child: Text(
                        "My Skill\nLevel",
                        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                      ),
                    ),
                    // Me and Average Progress Bars
                    Expanded(
                      flex: 6,
                      child: Text(
                        "Enter your skill level to see the results",
                        style: TextStyle(fontSize: 13,color: Colors.red),
                      ),
                    ),

                  ],
                ),
              ),
              Widgets.heightSpaceH1,
              Widgets.buildComparisonRow(
                label: "My\nCountry",
                meValue:
                    sessionController.longestData.value.userLongestRun ?? 0,
                averageValue: sessionController
                        .longestData.value.statistics?.country?.average ??
                    0,
                maxValue: sessionController
                        .longestData.value.statistics?.country?.maximum ??
                    0,
              ),
              Widgets.heightSpaceH1,

              Widgets.buildComparisonRow(
                label: "World",
                meValue:
                    sessionController.longestData.value.userLongestRun ?? 0,
                averageValue: sessionController
                        .longestData.value.statistics?.world?.average ??
                    0,
                maxValue: sessionController
                        .longestData.value.statistics?.world?.maximum ??
                    0,
              ),

              Divider(
                color: Colors.grey.shade200,
              ),
              Widgets.heightSpaceH1,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Widgets.buildLegendItem(Colors.cyan, "Me"),
                  Widgets.buildLegendItem(Colors.grey.shade300, "Average"),
                  Widgets.buildLegendItem(Colors.orangeAccent, "Maximum"),
                ],
              ),
            ],
          ));
    });
  }

  numberOfSessions() {
    return Obx(() {
      return Container(
          width: 1.sw,
          padding: const EdgeInsets.all(15),
          decoration: Widgets.blockDecoration,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Texts.textBlock("Number Of Sessions",
                  fontFamily: "RobotoRegular",
                  size: 17,
                  fontWeight: FontWeight.w300),

              Widgets.heightSpaceH2,
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Previous month button
                  IconButton(
                    icon: const Icon(
                      Icons.arrow_back_ios,
                      color: Colors.black45,
                      size: 22,
                    ),
                    onPressed: sessionController.decrementYear,
                  ),

                  Text(
                    sessionController.year.value.toString(),
                    style: const TextStyle(fontSize: 20),
                  ),

                  IconButton(
                    icon: const Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.black45,
                      size: 22,
                    ),
                    onPressed: sessionController.incrementYear,
                  ),
                ],
              ),
              // Bar Chart
              sessionController.isNumberSessionLoading.value
                  ? LongestRunSkeleton()
                  : SizedBox(
                      height: 220,
                      child: Obx(
                        () => BarChart(
                          BarChartData(
                            alignment: BarChartAlignment.spaceAround,
                            barGroups: [
                              BarChartGroupData(
                                x: 0,
                                barRods: [
                                  BarChartRodData(
                                    toY: sessionController
                                        .numberSessionData.value.userSessions!
                                        .toDouble(),
                                    color: Colors.cyan,
                                    width: 50,
                                    borderRadius: BorderRadius.circular(100),
                                  ),
                                ],
                              ),
                              BarChartGroupData(
                                x: 1,
                                barRods: [
                                  BarChartRodData(
                                    toY: sessionController.numberSessionData
                                        .value.averageSessions!
                                        .toDouble(),
                                    color: Colors.grey.shade300,
                                    width: 50,
                                    borderRadius: BorderRadius.circular(100),
                                  ),
                                ],
                                showingTooltipIndicators: [],
                              ),
                              BarChartGroupData(
                                x: 2,
                                barRods: [
                                  BarChartRodData(
                                    toY: sessionController
                                        .numberSessionData.value.maxSessions!
                                        .toDouble(),
                                    color: Colors.amber,
                                    width: 50,
                                    borderRadius: BorderRadius.circular(100),
                                  ),
                                ],
                                showingTooltipIndicators: [],
                              ),
                            ],
                            titlesData: FlTitlesData(
                              bottomTitles: AxisTitles(
                                drawBelowEverything: false,
                                sideTitles: SideTitles(
                                  reservedSize: 40,
                                  showTitles: true,
                                  getTitlesWidget: (value, meta) {
                                    switch (value.toInt()) {
                                      case 0:
                                        return Padding(
                                          padding: EdgeInsets.only(top: 8.0),
                                          child: Column(
                                            children: [
                                              Column(
                                                children: [
                                                  Text(
                                                    sessionController
                                                        .numberSessionData
                                                        .value
                                                        .userSessions!
                                                        .toString(),
                                                    style: TextStyle(
                                                        fontSize: 14,
                                                        color: Colors.black,
                                                        fontWeight:
                                                            FontWeight.w800),
                                                  ),
                                                ],
                                              ),
                                              Text(
                                                'Me',
                                                style: TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.black54),
                                              ),
                                            ],
                                          ),
                                        );
                                      case 1:
                                        return Padding(
                                            padding:
                                                const EdgeInsets.only(top: 8.0),
                                            child: Column(
                                              children: [
                                                Text(
                                                  sessionController
                                                      .numberSessionData
                                                      .value
                                                      .averageSessions!
                                                      .toString(),
                                                  style: TextStyle(
                                                      fontSize: 14,
                                                      color: Colors.black,
                                                      fontWeight:
                                                          FontWeight.w800),
                                                ),
                                                Text(
                                                  'Average',
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      color: Colors.black54),
                                                ),
                                              ],
                                            ));
                                      case 2:
                                        return Padding(
                                          padding: EdgeInsets.only(top: 8.0),
                                          child: Column(
                                            children: [
                                              Text(
                                                sessionController
                                                    .numberSessionData
                                                    .value
                                                    .maxSessions!
                                                    .toString(),
                                                style: TextStyle(
                                                    fontSize: 14,
                                                    color: Colors.black,
                                                    fontWeight:
                                                        FontWeight.w800),
                                              ),
                                              Text(
                                                'Max',
                                                style: TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.black54),
                                              ),
                                            ],
                                          ),
                                        );
                                      default:
                                        return const SizedBox(); // Empty for other values
                                    }
                                  },
                                ),
                              ),
                              leftTitles: const AxisTitles(
                                drawBelowEverything: false,
                                sideTitles: SideTitles(
                                  showTitles: false,
                                ),
                              ),
                              rightTitles: const AxisTitles(
                                drawBelowEverything: false,
                                sideTitles: SideTitles(
                                  showTitles: false,
                                ),
                              ),
                              topTitles: const AxisTitles(
                                drawBelowEverything: false,
                                sideTitles: SideTitles(
                                  showTitles: false,
                                ),
                              ),
                            ),
                            borderData: FlBorderData(show: false),
                            gridData: const FlGridData(
                              show: false, // Disable grid lines
                            ),
                          ),
                        ),
                      ),
                    )
            ],
          ));
    });
  }

  pumpsMood() {
    return Container(
        width: 1.sw,
        padding: const EdgeInsets.all(15),
        decoration: Widgets.blockDecoration,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Texts.textBlock("Pumps & Mood",
                fontFamily: "RobotoRegular",
                size: 17,
                fontWeight: FontWeight.w300),
            Widgets.heightSpaceH2,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    Texts.textBlock(
                        sessionController.trackGraphData?.overallHours ?? "0",
                        fontFamily: "RobotoRegular",
                        size: 40,
                        fontWeight: FontWeight.w100,
                        color: ColorConstants.secondaryColor),
                    Texts.textNormal("Overall \nHours",
                        fontFamily: "RobotoRegular",
                        size: 11,
                        color: Colors.black54),
                  ],
                ),
                Container(
                  width: 1,
                  height: 70,
                  color: ColorConstants.primaryColor,
                ),
                Column(
                  children: [
                    Texts.textBlock(
                        sessionController.trackGraphData?.currentMonthHours ??
                            "0",
                        fontFamily: "RobotoRegular",
                        size: 40,
                        fontWeight: FontWeight.w100,
                        color: ColorConstants.secondaryColor),
                    Texts.textNormal("Hours this \nmonth",
                        fontFamily: "RobotoRegular",
                        size: 11,
                        color: Colors.black54),
                  ],
                )
              ],
            ),
            Widgets.heightSpaceH1,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Previous month button
                IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: Colors.black45,
                    size: 17,
                  ),
                  onPressed: goToPreviousMonth,
                ),

                // Display current month and year
                Text(
                  formattedMonthYear ?? "",
                  style: const TextStyle(fontSize: 15),
                ),

                // Next month button
                IconButton(
                  icon: const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.black45,
                    size: 17,
                  ),
                  onPressed: goToNextMonth,
                ),
              ],
            ),
            sessionController.trackGraphData != null
                ? graphSection()
                : const SizedBox(),
            Widgets.heightSpaceH1,
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Texts.textMedium("Overall Starts this months: ",
                        fontFamily: "RobotoRegular",
                        size: 13,
                        color: Colors.black54),
                    Texts.textMedium(
                        sessionController.trackGraphData?.totalStarts ?? "0",
                        fontFamily: "RobotoRegular",
                        size: 14,
                        color: ColorConstants.secondaryColor)
                  ],
                )
              ],
            )
          ],
        ));
  }

  graphSection() {
    return SizedBox(
      width: double.infinity,
      height: 200,
      child: SfCartesianChart(
        plotAreaBorderWidth: 0,
        margin: const EdgeInsets.all(0),
        primaryXAxis: CategoryAxis(
          isVisible: true, labelStyle: const TextStyle(fontSize: 8),
          axisLine: const AxisLine(width: .4), // Remove axis border
          majorGridLines:
              const MajorGridLines(width: 0), // Remove grid lines on X-axis
        ),
        primaryYAxis: NumericAxis(
          labelStyle: const TextStyle(fontSize: 8),
          title: AxisTitle(
              text: 'hrs',
              textStyle: const TextStyle(fontSize: 10),
              alignment: ChartAlignment.near), // Y-axis label
          axisLine: const AxisLine(width: .4), // Remove axis border
          majorGridLines:
              const MajorGridLines(width: 0), // Remove grid lines on Y-axis
        ),
        series: <CartesianSeries>[
          ColumnSeries<SessionData, String>(
            dataSource: sessionController.populateChartData(),
            xValueMapper: (SessionData data, _) => data.weekday,
            yValueMapper: (SessionData data, _) => data.hours,
            pointColorMapper: (SessionData data, _) => Colors.cyan,
            // Set a fixed spacing between bars
            borderRadius: const BorderRadius.all(Radius.circular(50)),
          ),
          LineSeries<SessionData, String>(
            dataSource: sessionController.populateChartData(),
            xValueMapper: (SessionData data, _) => data.weekday,
            yValueMapper: (SessionData data, _) => data.mood,
            color: Colors.red,
          ),
        ],
        annotations: <CartesianChartAnnotation>[
          CartesianChartAnnotation(
            widget: const Text('😊', style: TextStyle(fontSize: 24)),
            coordinateUnit: CoordinateUnit.point,
            x: sessionController.trackGraphData?.graphData?.bestMood?.date ??
                "",
            y: sessionController.trackGraphData?.graphData?.bestMood?.level ??
                "",
          )
        ],
      ),
    );
  }
}
