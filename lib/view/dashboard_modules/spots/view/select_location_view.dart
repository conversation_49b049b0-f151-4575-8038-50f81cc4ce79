import 'package:flutter/material.dart';
import 'package:foilup/core/widgets/primary_button.dart';
import 'package:foilup/view/dashboard_modules/spots/controller/spot_controller.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/utils/permission_manager.dart';
import '../controller/add_spot_controller.dart';

class SelectLocationView extends StatefulWidget {
  const SelectLocationView({super.key});

  @override
  _SelectLocationViewState createState() => _SelectLocationViewState();
}

class _SelectLocationViewState extends State<SelectLocationView> {
  GoogleMapController? googleMapController;
  LatLng selectedLocation = const LatLng(37.7749, -122.4194);
  LatLng? currentLocation;
  late AddSportController addSpotController;

  @override
  void initState() {
    super.initState();
    addSpotController = Get.find();

    moveCurrentLocation();
  } // Default to San Francisco

  Future<void> moveCurrentLocation() async {
    // Use PermissionManager to avoid opening settings unexpectedly
    bool serviceEnabled = await PermissionManager.isLocationServiceEnabled();
    if (!serviceEnabled) {
      Get.snackbar(
        'Location Services Disabled',
        'Please enable location services in your device settings to use this feature.',
        snackPosition: SnackPosition.BOTTOM,
      );
      return Future.error('Location services are disabled.');
    }

    // Request permission using the new manager
    LocationPermission permission = await PermissionManager.requestLocationPermission(
      context: 'Select your current location for the new spot',
      showDialog: true,
    );

    if (permission != LocationPermission.always &&
        permission != LocationPermission.whileInUse) {
      return Future.error('Location permissions are denied');
    }

    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    // Get the current position
    Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high);

    setState(() {
      currentLocation = LatLng(position.latitude, position.longitude);
    });

    // Move the camera to the current location
    googleMapController
        ?.animateCamera(CameraUpdate.newLatLng(currentLocation!));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back_ios_new,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Select Location",
            size: 20, fontWeight: FontWeight.w700),
        actions: const [],
      ),
      body: Stack(
        children: [
          GoogleMap(
            mapType: MapType.hybrid,
            myLocationEnabled: true,
            myLocationButtonEnabled: true,
            initialCameraPosition: CameraPosition(
              target: selectedLocation,
              zoom: 14.0,
            ),
            onMapCreated: (GoogleMapController controller) {
              googleMapController = controller;
            },
            onTap: onMapTapped,
            markers: {
              Marker(
                markerId: const MarkerId('selectedLocation'),
                position: selectedLocation,
              ),
            },
          ),
          Positioned(
            bottom: 16.0,
            left: 16.0,
            child: PrimaryButton(
                title: "Confirm Location",
                callback: () {
                  addSpotController.selectLocationFromMap(selectedLocation);
                }),
          ),
        ],
      ),
    );
  }

  void onMapTapped(LatLng location) {
    setState(() {
      selectedLocation = location;
    });
  }
}
