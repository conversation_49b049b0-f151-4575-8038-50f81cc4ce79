name: foilup
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  get: ^4.6.1
  shimmer: ^3.0.0
  shared_preferences: ^2.0.12
  flutter_screenutil: ^5.7.0
  flutter_easyloading: ^3.0.3
  flutter_image_compress: ^2.3.0
  readmore:
  animated_splash_screen: ^1.3.0
  translator: ^1.0.3+1
  geolocator: ^13.0.1
  loading_indicator: ^3.1.0
  webview_flutter: ^4.8.0

  flutter_advanced_avatar: ^1.5.0
  permission_handler:
  animation_wrappers: ^3.0.0
  country_picker: ^2.0.16
  google_maps_flutter: ^2.9.0
  responsive_sizer: ^3.3.1
  dotted_border:
  image_picker: ^1.1.2
  custom_rating_bar: ^3.0.0
  map_launcher: ^3.5.0
  like_button: ^2.0.5
  timeago:
  flutter_svg:
  flutter_staggered_grid_view: ^0.7.0
  flutter_launcher_icons: ^0.13.1
  avatar_stack: ^1.2.0
  firebase_messaging:
  firebase_core:
  awesome_notifications: ^0.9.3+1
  fl_chart: ^0.69.0
  syncfusion_flutter_charts:
  qr_flutter: ^4.1.0
  share_plus: ^10.1.2
  dio: ^5.4.3+1
  path_provider: ^2.1.5
  cached_network_image: ^3.2.3
  intl: ^0.19.0
  pin_code_fields: ^8.0.1
  photo_view: ^0.15.0
  url_launcher:
  auto_size_text: ^3.0.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/android_app_icon.jpg"
  remove_alpha_ios: true
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/
    - assets/icons/
    - assets/images/
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Black.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
        - asset: assets/fonts/Roboto-Medium.ttf
          style: italic
    - family: RobotoRegular
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf


  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
